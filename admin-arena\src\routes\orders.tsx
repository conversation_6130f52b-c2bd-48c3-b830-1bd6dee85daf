// Orders route with permission-based access
// Requires order management permissions

import { createFileRoute, redirect } from '@tanstack/react-router'
import { OrdersListPage } from '../pages/orders/OrdersListPage'
import { AuthService } from '../services/auth-service'

export const Route = createFileRoute('/orders')({
  beforeLoad: async () => {
    // In pure cookie-based auth, check with server
    try {
      await AuthService.getCurrentUser()
      // If this succeeds, user is authenticated
    } catch (error) {
      // If this fails, user is not authenticated
      throw redirect({ to: '/login' })
    }
    // Note: Permission checking will be handled by the component
    // since we need to load user data first
  },
  component: OrdersComponent,
})

function OrdersComponent() {
  // Component will handle permission checking after auth is confirmed
  return <OrdersListPage />
}
