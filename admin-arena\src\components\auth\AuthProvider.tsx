// Pure cookie-based authentication provider
// Server handles all token management via HTTP-only cookies
// No frontend token refresh needed

import React, { useEffect } from 'react'
import { useAuth } from '../../hooks/use-auth'

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const { getCurrentUser, isAuthenticated } = useAuth()

  useEffect(() => {
    // Try to get current user on app load
    // If HTTP-only cookies exist, this will succeed
    const initializeAuth = async () => {
      if (!isAuthenticated) {
        try {
          await getCurrentUser()
        } catch (error) {
          // If this fails, user is not authenticated
          // No need to log error as it's expected for unauthenticated users
          console.debug('User not authenticated on app load')
        }
      }
    }

    initializeAuth()
  }, [getCurrentUser, isAuthenticated])

  // No token refresh interval needed - server handles this automatically
  // HTTP-only cookies are refreshed server-side when needed

  return <>{children}</>
}
