// Pure cookie-based API client for admin arena
// HTTP-only cookies are sent automatically by the browser
// No token management needed on frontend

import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios'
import { AuthStorage } from '../utils/auth-storage'
import { PaginatedResponse } from '../types/api-types'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8000'

// Create the main axios instance with cookie support
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true, // Important: This sends HTTP-only cookies automatically
  headers: {
    'Content-Type': 'application/json',
  },
})

// No request interceptor needed - HTTP-only cookies are sent automatically

// Response interceptor for error handling
// Token refresh is handled server-side automatically
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean }

    // Handle 401 errors - try token refresh once
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        // Attempt server-side token refresh (cookies handled automatically)
        const refreshResponse = await axios.post(
          `${API_BASE_URL}/api/staff/auth/token/refresh/`,
          {},
          { withCredentials: true }
        )

        if (refreshResponse.data.success) {
          // Server has updated HTTP-only cookies, retry original request
          return apiClient(originalRequest)
        } else {
          throw new Error('Token refresh failed')
        }
      } catch (refreshError) {
        // Refresh failed, clear any legacy cookies and redirect to login
        AuthStorage.clearTokens()

        // Only redirect if we're not already on the login page
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }

        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  }
)

/**
 * Generic API client class for CRUD operations
 * Based on react-ts-client APIClient pattern
 */
class APIClient<TResponse, TRequest = TResponse> {
  endpoint: string

  constructor(endpoint: string) {
    this.endpoint = endpoint
  }

  /**
   * GET single resource
   */
  get = async (config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.get<TResponse>(this.endpoint, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw error
    }
  };

  /**
   * GET paginated list of resources
   */
  getAll = async (config?: AxiosRequestConfig): Promise<PaginatedResponse<TResponse>> => {
    try {
      const response = await apiClient.get<PaginatedResponse<TResponse>>(this.endpoint, config)
      console.log(`API Call: ${response.config.url}`)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw error
    }
  };

  /**
   * POST create new resource
   */
  post = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.post<TResponse>(this.endpoint, data, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw error
    }
  };

  /**
   * PATCH partial update resource
   */
  patch = async (data: Partial<TRequest>, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.patch<TResponse>(this.endpoint, data, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw error
    }
  };

  /**
   * PUT full update resource
   */
  put = async (data: TRequest, config?: AxiosRequestConfig): Promise<TResponse> => {
    try {
      const response = await apiClient.put<TResponse>(this.endpoint, data, config)
      return response.data
    } catch (error) {
      this.handleError(error)
      throw error
    }
  };

  /**
   * DELETE resource
   */
  delete = async (itemId?: number | string): Promise<void> => {
    try {
      const url = itemId ? `${this.endpoint}${itemId}/` : this.endpoint
      await apiClient.delete(url)
    } catch (error) {
      this.handleError(error)
      throw error
    }
  };

  /**
   * Handle API errors with proper logging and formatting
   */
  private handleError = (error: unknown): void => {
    if (axios.isAxiosError(error)) {
      // Don't log cancelled requests as errors
      if (error.code === 'ERR_CANCELED' || error.message === 'canceled') {
        throw error
      }

      // Don't log expected authentication errors to reduce console noise
      const isAuthError = error.response?.status === 401 || error.response?.status === 403

      if (!isAuthError) {
        console.error('API Error:', error.message)
        if (error.response) {
          console.error('Response data:', error.response.data)
          console.error('Response status:', error.response.status)
        }
      }

      // Format error for consistent handling
      throw {
        message: error.message,
        response: error.response,
        status: error.response?.status,
        data: error.response?.data
      }
    }

    console.error('Unexpected error:', error)
    throw error
  };
}

export default APIClient
