// Sidebar navigation styles
// Responsive sidebar with smooth transitions

@use '../../scss/variables' as vars;
@use '../../scss/mixins' as mixins;

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: vars.$sidebar-width;
  background: white;
  border-right: 1px solid vars.$gray-200;
  box-shadow: vars.$shadow-sm;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease, transform 0.3s ease;
  z-index: vars.$z-fixed;

  &.collapsed {
    width: vars.$sidebar-collapsed-width;
  }

  @include mixins.mobile-only {
    transform: translateX(-100%);

    &.mobileOpen {
      transform: translateX(0);
    }
  }
}

.mobileOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: vars.$z-modal-backdrop;

  @include mixins.desktop-only {
    display: none;
  }
}

.header {
  @include mixins.flex-between;
  padding: vars.$spacing-4;
  border-bottom: 1px solid vars.$gray-200;
  min-height: vars.$header-height;
}

.logo {
  flex: 1;
  min-width: 0;
}

.logoFull {
  font-size: vars.$font-size-xl;
  font-weight: vars.$font-weight-bold;
  color: vars.$primary-600;
  @include mixins.truncate;
}

.logoCollapsed {
  font-size: vars.$font-size-lg;
  font-weight: vars.$font-weight-bold;
  color: vars.$primary-600;
  text-align: center;
  display: block;
}

.toggleButton {
  @include mixins.flex-center;
  width: vars.$spacing-8;
  height: vars.$spacing-8;
  background: none;
  border: none;
  border-radius: vars.$border-radius;
  color: vars.$gray-600;
  cursor: pointer;
  transition: vars.$transition-colors;

  &:hover {
    background-color: vars.$gray-100;
    color: vars.$gray-900;
  }

  &:focus {
    outline: 2px solid vars.$primary-500;
    outline-offset: 2px;
  }

  svg {
    width: vars.$spacing-5;
    height: vars.$spacing-5;
  }

  @include mixins.mobile-only {
    display: none;
  }
}

.mobileCloseButton {
  @include mixins.flex-center;
  width: vars.$spacing-8;
  height: vars.$spacing-8;
  background: none;
  border: none;
  border-radius: vars.$border-radius;
  color: vars.$gray-600;
  cursor: pointer;
  transition: vars.$transition-colors;

  &:hover {
    background-color: vars.$gray-100;
    color: vars.$gray-900;
  }

  svg {
    width: vars.$spacing-5;
    height: vars.$spacing-5;
  }

  @include mixins.desktop-only {
    display: none;
  }
}

.navigation {
  flex: 1;
  padding: vars.$spacing-4 0;
  overflow-y: auto;
}

.navigationList {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: vars.$spacing-1;
}

.navigationItem {
  padding: 0 vars.$spacing-4;
}

.navigationLink {
  @include mixins.flex-start;
  gap: vars.$spacing-3;
  padding: vars.$spacing-3;
  border-radius: vars.$border-radius;
  color: vars.$gray-700;
  text-decoration: none;
  transition: vars.$transition-colors;
  width: 100%;

  &:hover {
    background-color: vars.$gray-100;
    color: vars.$gray-900;
  }

  &:focus {
    outline: 2px solid vars.$primary-500;
    outline-offset: 2px;
  }

  &.active {
    background-color: vars.$primary-50;
    color: vars.$primary-700;

    .icon {
      color: vars.$primary-600;
    }
  }

  .collapsed & {
    justify-content: center;
    padding: vars.$spacing-3 vars.$spacing-2;
  }
}

.icon {
  width: vars.$spacing-5;
  height: vars.$spacing-5;
  flex-shrink: 0;
  color: vars.$gray-500;
  transition: vars.$transition-colors;
}

.label {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  @include mixins.truncate;

  .collapsed & {
    display: none;
  }
}

.footer {
  padding: vars.$spacing-4;
  border-top: 1px solid vars.$gray-200;

  .collapsed & {
    padding: vars.$spacing-2;
  }
}

.userInfo {
  @include mixins.flex-start;
  gap: vars.$spacing-3;

  .collapsed & {
    justify-content: center;
  }
}

.userAvatar {
  @include mixins.flex-center;
  width: vars.$spacing-8;
  height: vars.$spacing-8;
  background: linear-gradient(135deg, vars.$primary-500, vars.$primary-600);
  color: white;
  border-radius: vars.$border-radius-full;
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-semibold;
  flex-shrink: 0;
}

.userDetails {
  @include mixins.flex-column;
  gap: vars.$spacing-0-5;
  min-width: 0;

  .collapsed & {
    display: none;
  }
}

.userName {
  font-size: vars.$font-size-sm;
  font-weight: vars.$font-weight-medium;
  color: vars.$gray-900;
  @include mixins.truncate;
}

.userRole {
  font-size: vars.$font-size-xs;
  color: vars.$gray-500;
  @include mixins.truncate;
}

// Responsive adjustments
@include mixins.responsive(lg) {
  .sidebar {
    position: fixed;
    transform: translateX(0);
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .sidebar {
    transition: none;
  }

  .navigationLink {
    transition: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .sidebar {
    border-right: 2px solid vars.$gray-900;
  }

  .navigationLink {
    &.active {
      border: 2px solid vars.$primary-600;
    }
  }
}