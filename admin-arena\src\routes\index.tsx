// Dashboard home route
// Main landing page for authenticated admin users

import { createFileRoute, redirect } from '@tanstack/react-router'
import { DashboardPage } from '../pages/dashboard/DashboardPage'
import { AuthService } from '../services/auth-service'

export const Route = createFileRoute('/')({
  beforeLoad: async () => {
    // In pure cookie-based auth, we need to check with the server
    // HTTP-only cookies will be sent automatically
    try {
      await AuthService.getCurrentUser()
      // If this succeeds, user is authenticated
    } catch (error) {
      // If this fails, user is not authenticated
      throw redirect({ to: '/login' })
    }
  },
  component: DashboardPage,
})
