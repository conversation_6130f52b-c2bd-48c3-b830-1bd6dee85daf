@use './variables' as *;

// Generic flexbox mixin
@mixin flexbox($justify: center, $align: center, $direction: row, $wrap: nowrap) {
  display: flex;
  justify-content: $justify;
  align-items: $align;
  flex-direction: $direction;
  flex-wrap: $wrap;
}

@mixin btn($color, $bg-color) {
  @include flexbox(center, center);
  color: $color;
  background-color: $bg-color;
  padding: 5px 10px;
  border-radius: 3px;
}

// @mixin theme($light-theme: true) {
//   @if $light-theme {
//     background-color: lighten($primary-dark, 100%);
//     color: darken($text-color, 100%);
//   }
// }

// add this class 
// .light {
//   @include theme(true);
//   // @include theme($light-theme: true);
// }


@mixin mobile {
  @media (max-width: $mobile) {
    @content;
  }
}

// Example usage of mobile mixin:
// @include mobile {
//   flex-direction: column;
// }

// Dynamic Mixin for List Styling
@mixin levelStyles($max-levels, $base-color: #000, $gap: 10px) {
  @for $i from 0 through $max-levels {
    .level-#{$i} {
      margin-left: $i * $gap;
      list-style: circle;
      color: darken($base-color, $i * 10%);

      // Example: Different list styles for different levels
      @if $i % 3==0 {
        list-style: disc;
      }

      @else if $i % 3==1 {
        list-style: square;
      }

      @else {
        list-style: none;
      }

      // Adjust for flex styles
      @if $i ==0 {
        @include flexbox(flex-start, flex-start, row);
        flex-wrap: wrap;
      }

      @else {
        display: block;
      }
    }
  }
}



// Extensions in scss

// Admin-specific mixins for consistent styling

// Enhanced Layout Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin flex-column-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

// Text Utilities
@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin line-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// Button Mixins
@mixin button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: $border-radius;
  font-family: $font-family-sans;
  font-weight: $font-weight-medium;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
  }
}

@mixin button-size($padding-y, $padding-x, $font-size) {
  padding: $padding-y $padding-x;
  font-size: $font-size;
}

// Input Mixins
@mixin input-base {
  display: block;
  width: 100%;
  padding: $spacing-3 $spacing-4;
  border: 1px solid $gray-300;
  border-radius: $border-radius;
  font-family: $font-family-sans;
  font-size: $font-size-sm;
  background-color: white;
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;

  &:focus {
    outline: none;
    border-color: $primary-500;
    box-shadow: 0 0 0 3px rgba($primary-500, 0.1);
  }

  &:disabled {
    background-color: $gray-50;
    cursor: not-allowed;
  }
}

// Card Mixins
@mixin card {
  background: white;
  border: 1px solid $gray-200;
  border-radius: $border-radius-lg;
  box-shadow: $shadow-sm;
}

// Table Mixins
@mixin table-base {
  width: 100%;
  border-collapse: collapse;
  background: white;

  th,
  td {
    padding: $spacing-3 $spacing-4;
    text-align: left;
    border-bottom: 1px solid $gray-200;
  }

  th {
    background-color: $gray-50;
    font-weight: $font-weight-semibold;
    font-size: $font-size-sm;
    color: $gray-900;
  }

  tbody tr {
    transition: background-color 0.2s ease-in-out;

    &:hover {
      background-color: $gray-50;
    }
  }
}

// Responsive Mixins
@mixin responsive($breakpoint) {
  @if $breakpoint ==sm {
    @media (min-width: $breakpoint-sm) {
      @content;
    }
  }

  @if $breakpoint ==md {
    @media (min-width: $breakpoint-md) {
      @content;
    }
  }

  @if $breakpoint ==lg {
    @media (min-width: $breakpoint-lg) {
      @content;
    }
  }

  @if $breakpoint ==xl {
    @media (min-width: $breakpoint-xl) {
      @content;
    }
  }
}

@mixin mobile-only {
  @media (max-width: #{$breakpoint-lg - 1px}) {
    @content;
  }
}

@mixin desktop-only {
  @media (min-width: $breakpoint-lg) {
    @content;
  }
}