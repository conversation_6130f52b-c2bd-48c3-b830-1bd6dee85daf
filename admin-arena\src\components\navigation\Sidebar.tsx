// Sidebar navigation component
// Role-based navigation with collapsible design

import React from 'react'
import { Link, useLocation } from '@tanstack/react-router'
import {
  FiHome,
  FiShoppingCart,
  FiPackage,
  FiUsers,
  FiBarChart,
  FiSettings,
  FiMenu,
  FiX
} from 'react-icons/fi'
import { useAuth } from '../../hooks/use-auth'
import { useSidebar } from '../../stores/ui-store'
import styles from './Sidebar.module.scss'

interface NavigationItem {
  id: string
  label: string
  path: string
  icon: React.ComponentType
  permission?: string
  group?: string
}

const navigationItems: NavigationItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    path: '/',
    icon: FiHome,
  },
  {
    id: 'orders',
    label: 'Orders',
    path: '/orders',
    icon: FiShoppingCart,
    permission: 'staff.view_orderproxy',
  },
  {
    id: 'products',
    label: 'Products',
    path: '/products',
    icon: FiPackage,
    permission: 'staff.view_productproxy',
  },
  {
    id: 'customers',
    label: 'Customers',
    path: '/customers',
    icon: FiUsers,
    permission: 'staff.view_customerproxy',
  },
  {
    id: 'analytics',
    label: 'Analytics',
    path: '/analytics',
    icon: FiBarChart,
    permission: 'staff.view_analytics',
  },
  {
    id: 'settings',
    label: 'Settings',
    path: '/settings',
    icon: FiSettings,
  },
]

export const Sidebar: React.FC = () => {
  const location = useLocation()
  const { user, checkPermission, hasGroup } = useAuth()
  const {
    sidebarCollapsed,
    sidebarMobileOpen,
    toggleSidebar,
    toggleMobileSidebar
  } = useSidebar()

  const isItemVisible = (item: NavigationItem): boolean => {
    // Superuser can see everything
    if (user?.is_superuser) return true

    // Check group membership
    if (item.group && !hasGroup(item.group)) return false

    // Check permission
    if (item.permission && !checkPermission(item.permission)) return false

    return true
  }

  const isItemActive = (path: string): boolean => {
    if (path === '/') {
      return location.pathname === '/'
    }
    return location.pathname.startsWith(path)
  }

  const renderNavigationItem = (item: NavigationItem) => {
    if (!isItemVisible(item)) return null

    const isActive = isItemActive(item.path)

    return (
      <li key={item.id} className={styles.navigationItem}>
        <Link
          to={item.path}
          className={`${styles.navigationLink} ${isActive ? styles.active : ''}`}
          title={sidebarCollapsed ? item.label : undefined}
        >
          <item.icon className={styles.icon} />
          {!sidebarCollapsed && (
            <span className={styles.label}>{item.label}</span>
          )}
        </Link>
      </li>
    )
  }

  return (
    <>
      {/* Mobile Overlay */}
      {sidebarMobileOpen && (
        <div
          className={styles.mobileOverlay}
          onClick={toggleMobileSidebar}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        ${styles.sidebar} 
        ${sidebarCollapsed ? styles.collapsed : ''} 
        ${sidebarMobileOpen ? styles.mobileOpen : ''}
      `}>
        <div className={styles.header}>
          <div className={styles.logo}>
            {sidebarCollapsed ? (
              <span className={styles.logoCollapsed}>AA</span>
            ) : (
              <span className={styles.logoFull}>Admin Arena</span>
            )}
          </div>

          <button
            className={styles.toggleButton}
            onClick={toggleSidebar}
            aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <FiMenu />
          </button>

          <button
            className={styles.mobileCloseButton}
            onClick={toggleMobileSidebar}
            aria-label="Close sidebar"
          >
            <FiX />
          </button>
        </div>

        <nav className={styles.navigation}>
          <ul className={styles.navigationList}>
            {navigationItems.map(renderNavigationItem)}
          </ul>
        </nav>

        <div className={styles.footer}>
          {!sidebarCollapsed && user && (
            <div className={styles.userInfo}>
              <div className={styles.userAvatar}>
                {user.email.charAt(0).toUpperCase()}
              </div>
              <div className={styles.userDetails}>
                <span className={styles.userName}>
                  {user.staff_profile?.full_name || user.email}
                </span>
                <span className={styles.userRole}>
                  {user.staff_profile?.position_title || 'Admin'}
                </span>
              </div>
            </div>
          )}
        </div>
      </aside>
    </>
  )
}
