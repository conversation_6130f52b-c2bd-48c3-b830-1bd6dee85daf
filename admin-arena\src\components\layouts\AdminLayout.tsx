// Main admin layout component
// Provides sidebar navigation and header for authenticated users

import React from 'react';
import { useLocation } from '@tanstack/react-router';
import { useAuth } from '../../hooks/use-auth';
import { useSidebar } from '../../stores/ui-store';
import { Sidebar } from '../navigation/Sidebar';
import { Header } from '../navigation/Header';
import styles from './AdminLayout.module.scss';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated } = useAuth();
  const { sidebarCollapsed } = useSidebar();

  // Don't show admin layout on login page
  const isLoginPage = location.pathname === '/login';
  
  if (isLoginPage || !isAuthenticated) {
    return <>{children}</>;
  }

  return (
    <div className={styles.layout}>
      <Sidebar />
      
      <div className={`${styles.main} ${sidebarCollapsed ? styles.sidebarCollapsed : ''}`}>
        <Header />
        
        <main className={styles.content}>
          {children}
        </main>
      </div>
    </div>
  );
};
